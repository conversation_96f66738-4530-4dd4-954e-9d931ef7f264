## Help

所有可用命令都可以通过运行 `make` 命令查看

```sh
make
```


## Configurations

| Config                                            | Required   | Help                              |
|---------------------------------------------------|------------|-----------------------------------|
| @env:DJANGO_DB_HOST                               | Yes        |                                   |
| @env:DJANGO_DB_NAME                               | Yes        |                                   |
| @env:DJANGO_DB_PASSWORD                           | Yes        |                                   |
| @env:DJANGO_DB_PORT                               | Yes        |                                   |
| @env:DJANGO_DB_USER                               | Yes        |                                   |
| @env:IMGPROXY_URL                                 | Yes        |                                   |
| @env:RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE         | Yes        | cooknife 服务的 CLIP 标签队列     |
| @env:RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE   | Yes        | cooknife 服务的 CLIP 标签回复队列 |
| @env:RABBITMQ_COOKNIFE_URL                        | Yes        | cooknife 服务的 RabbitMQ 连接地址 |
| @env:RABBITMQ_COOKNIFE_WALLPAPER_INBOUND_EXCHANGE | Yes        | cooknife 服务的壁纸入库交换机     |
| @env:RABBITMQ_URL                                 | Yes        |                                   |
| @env:RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE       | Yes        |                                   |
| @env:RABBITMQ_WALLPAPERS_INBOUND_QUEUE            | Yes        |                                   |
| @env:CLOUDFLARE_TURNSTILE_SECRET_KEY              | No         |                                   |
| @env:DJANGO_CONSOLE_LOGGING_LEVEL                 | No         |                                   |
| @env:DJANGO_CORS_ALLOWED_ORIGINS                  | No         |                                   |
| @env:DJANGO_CSRF_TRUSTED_ORIGINS                  | No         |                                   |
| @env:DJANGO_DB                                    | No         |                                   |
| @env:DJANGO_DEBUG                                 | No         |                                   |
| @env:DJANGO_ENV                                   | No         |                                   |
| @env:DJANGO_SENTRY_DSN                            | No         |                                   |
| @env:DJANGO_SERVICE                               | No         |                                   |
| @env:IMGPROXY_KEY                                 | No         |                                   |
| @env:IMGPROXY_SALT                                | No         |                                   |
