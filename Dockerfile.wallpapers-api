FROM python:3.12.3-slim

RUN apt-get update && apt-get install -y --no-install-recommends git

WORKDIR /code

RUN pip install -U pip

COPY requirements/django.txt requirements.txt

RUN pip install -r requirements.txt uvicorn

COPY . /code

ENV DJANGO_ENV=production
ENV DJANGO_SERVICE=wallpapers-api

RUN python manage.py collectstatic

CMD [ "uvicorn", "--host=0.0.0.0",  "--port=8000", "--lifespan=off", "project.asgi:application" ]
