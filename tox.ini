[tox]
no_package = true
env_list: test,collectstatic,pre-commit

[testenv]
deps = 
    -r requirements/django.txt

[testenv:test]
deps = 
    -r requirements/django.txt
    pytest
    pytest-django
    pytest-env
    schemathesis
setenv =
    PYTHONPATH = {toxinidir}
commands = pytest -x

[testenv:collectstatic]
; 容器构建时会使用该命令，该命令可能会在未提供环境变量的情况下失败
commands = python manage.py collectstatic --no-input

[testenv:pre-commit]
deps = pre-commit
commands = pre-commit run --all-files
