import functools
from inspect import iscoroutinefunction

import configat
import httpx
import zangar as z
from django.conf import settings
from django.http import HttpResponse

import openapi

_SECRET_KEY = configat.resolve(
    "@env:CLOUDFLARE_TURNSTILE_SECRET_KEY", "1x0000000000000000000000000000000AA"
)
_CF_TURNSTILE_HEADER = settings.CF_TURNSTILE_HEADER
_CF_TURNSTILE_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify"


def _verify_sync(response_token: str) -> bool:
    with httpx.Client() as client:  # pragma: no cover
        resp = client.post(
            _CF_TURNSTILE_URL, data={"secret": _SECRET_KEY, "response": response_token}
        )
        return resp.json().get("success", False)


async def _verify_async(response_token: str) -> bool:
    async with httpx.AsyncClient() as client:  # pragma: no cover
        resp = await client.post(
            _CF_TURNSTILE_URL, data={"secret": _SECRET_KEY, "response": response_token}
        )
        return resp.json().get("success", False)


def check_tunstile(func):
    if iscoroutinefunction(func):

        async def wrapper(*args, cf_turnstile, **kwargs) -> HttpResponse:  # type: ignore
            if not await _verify_async(cf_turnstile):
                return HttpResponse(status=400)
            return await func(*args, **kwargs)

    else:

        def wrapper(*args, cf_turnstile, **kwargs):
            if not _verify_sync(cf_turnstile):
                return HttpResponse(status=400)
            return func(*args, **kwargs)

    wrapper = functools.wraps(func)(wrapper)
    wrapper = openapi.response(400)(wrapper)
    wrapper = openapi.response(406)(wrapper)
    wrapper = openapi.header("cf_turnstile", name=_CF_TURNSTILE_HEADER, schema=z.str())(
        wrapper
    )
    return wrapper
