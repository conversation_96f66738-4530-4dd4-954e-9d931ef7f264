import functools
from inspect import iscoroutinefunction

import configat
import httpx
import zangar as z
from django.conf import settings
from django.http import HttpRequest, HttpResponse

import openapi

SECRET_KEY = configat.resolve(
    "@env:CLOUDFLARE_TURNSTILE_SECRET_KEY", "1x0000000000000000000000000000000AA"
)

CF_TURNSTILE_HEADER = settings.CF_TURNSTILE_HEADER


def check_tunstile(func):
    rejected_status = 406
    if iscoroutinefunction(func):

        @openapi.header(False, name=CF_TURNSTILE_HEADER, schema=z.str())
        @openapi.response(rejected_status, description="Cloudflare Turnstile 验证失败")
        @functools.wraps(func)
        async def wrapper(_, request: HttpRequest, *args, **kwargs) -> HttpResponse:
            token = request.headers.get(CF_TURNSTILE_HEADER)
            if not token:
                return HttpResponse(status=rejected_status)

            url = "https://challenges.cloudflare.com/turnstile/v0/siteverify"
            async with httpx.AsyncClient() as client:
                r = await client.post(
                    url, data={"secret": SECRET_KEY, "response": token}
                )
                result = r.json()

            if not result["success"]:
                return HttpResponse(status=403)
            return await func(_, request, *args, **kwargs)

    else:
        raise NotImplementedError

    return wrapper
