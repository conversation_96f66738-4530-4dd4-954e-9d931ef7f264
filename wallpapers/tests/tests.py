import os
from unittest import mock

import PIL.Image
import pytest
import schemathesis
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse, reverse_lazy
from schemathesis.generation.case import Case

from project.wsgi import application
from wallpapers import models
from wallpapers.services import misc

User = get_user_model()

schema = schemathesis.openapi.from_wsgi(
    reverse("wallpapers-api:openapi"),
    application,
)


@schema.parametrize()
@pytest.mark.django_db(transaction=True)
@pytest.mark.usefixtures("mock_cloudflare_tunstile")
def test_api(case: Case):
    if case.media_type == "application/x-www-form-urlencoded" and isinstance(
        case.body, list
    ):
        # BUG: 这种生成的 case，不会被正确构建
        return

    case.call_and_validate()


class WallpapersAPITest(TestCase):
    def test_save_wallpaper(self):
        with open(
            os.path.join(os.path.dirname(__file__), "assets/fff.png"), "rb"
        ) as fp:
            content = fp.read()
            image = PIL.Image.open(fp)
            data = misc.WallpaperImageData(
                url="https://example.com/image.jpg",
                image=image,
                content=content,
            )
            with mock.patch(
                "wallpapers.services.misc.publish_wallpaper_inbound_event"
            ) as publish:
                obj = misc.save_wallpaper(data)
                args, kwargs = publish.call_args
                assert args[0] == obj

        self.assertEqual(obj.format, "png")
        self.assertEqual(obj.filesize, 1824)
        self.assertEqual(obj.width, 600)
        self.assertEqual(obj.height, 400)
        self.assertEqual(obj.pixels, 240000)
        self.assertEqual(obj.aspect_ratio, 1.5)

        # 二次保存，重复图片 - 应该返回相同的对象
        obj2 = misc.save_wallpaper(data)
        self.assertEqual(obj.pk, obj2.pk)  # 应该是同一个对象
        self.assertEqual(obj.content_md5, obj2.content_md5)  # MD5 应该相同


class TestAPIWithFixtures(TestCase):
    fixtures = ["test"]

    def test_wallpapers_resize(self):
        response = self.client.get(
            reverse("wallpapers-api:WallpaperListView") + "?size=12&size=aa"
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json(),
            {
                "errors": [
                    {"loc": [0], "msgs": ["Invalid value"]},
                    {"loc": [1], "msgs": ["Invalid value"]},
                ],
                "in": "query",
                "name": "size",
            },
        )

    def test_wallpapers(self):
        response = self.client.get(reverse("wallpapers-api:WallpaperListView"))
        # 只获取有上传者的壁纸
        assert response.json() == {
            "current_page": 1,
            "current_page_size": 10,
            "total": 2,
            "wallpapers": [
                {
                    "content_md5": "ea4d214b6d466d9a08ae20ba2c74d0be",
                    "filesize": 794385,
                    "format": "jpeg",
                    "height": 4000,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.pexels.com/photos/1270184/pexels-photo-1270184.jpeg",
                    },
                    "uploader": {
                        "name": "Tim",
                    },
                    "width": 6000,
                },
                {
                    "content_md5": "5437dc8c432e55886004b45a2834ceb8",
                    "filesize": 2050093,
                    "format": "jpeg",
                    "height": 6720,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.unsplash.com/photo-1749253894957-e95b399aa381",
                    },
                    "uploader": {
                        "name": "Sam",
                    },
                    "width": 4480,
                },
            ],
        }

    def test_wallpapers_related(self):
        response = self.client.get(
            reverse(
                "wallpapers-api:RelatedWallpapersAPI",
                kwargs={"key": "5437dc8c432e55886004b45a2834ceb8"},
            )
        )
        assert response.json() == {
            "related": [
                {
                    "content_md5": "ea4d214b6d466d9a08ae20ba2c74d0be",
                    "filesize": 794385,
                    "format": "jpeg",
                    "height": 4000,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.pexels.com/photos/1270184/pexels-photo-1270184.jpeg",
                    },
                    "uploader": {
                        "name": "Tim",
                    },
                    "width": 6000,
                },
            ],
        }


class WallpapersAdminTest(TestCase):
    username = "testuser"
    password = "testpass"

    def setUp(self):
        self.admin_user = User.objects.create_superuser(
            username=self.username, password=self.password, email=None
        )

    def test_upload_wallpapers(self):
        self.client.login(username=self.username, password=self.password)

        url = reverse("wallpapers-admin:upload-wallpapers")

        # GET
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # POST
        with mock.patch(
            "wallpapers.urls_admin.publish_wallpapers"
        ) as mock_publish_wallpapers:
            response = self.client.post(
                url,
                {
                    "urls": "https://example.com/image.jpg\n\r\nhttps://example.com/image2.jpg\r\n"
                },
            )

            # 验证 publish_messages 被调用，并且传入了正确的参数
            self.assertTrue(mock_publish_wallpapers.called)
            self.assertEqual(
                mock_publish_wallpapers.call_args.args,
                (
                    [
                        "https://example.com/image.jpg",
                        "https://example.com/image2.jpg",
                    ],
                ),
            )
            self.assertEqual(response.status_code, 200)


class TestTopicsPublishedAPI(TestCase):
    url = reverse_lazy("wallpapers-api:TopicsPublishedAPI")

    def test_get(self):
        client = models.Client.objects.create()
        topic = models.Topic.objects.create()
        models.TopicPublished.objects.create(topic=topic, client=client)
        response = self.client.get(self.url, headers={"Client-Id": str(client.pk)})
        assert response.json() == {
            "topics": [
                {
                    "title": "",
                }
            ]
        }


class TestTopicWallpapersAPI(TestCase):
    def test_get(self):
        topic = models.Topic.objects.create()
        wallpaper = models.WallpaperImage.objects.create(
            content_md5="1234567890",
            url="https://example.com/image.jpg",
            format="jpeg",
            width=100,
            height=100,
            aspect_ratio=1.0,
            pixels=10000,
            filesize=1000,
        )
        models.TopicWallpaper.objects.create(topic=topic, wallpaper=wallpaper)
        response = self.client.get(
            reverse("wallpapers-api:TopicWallpapersAPI", kwargs={"topic_id": topic.pk})
        )
        assert response.json() == {
            "wallpapers": [
                {
                    "content_md5": "1234567890",
                    "filesize": 1000,
                    "format": "jpeg",
                    "height": 100,
                    "width": 100,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/example.com/image.jpg",
                    },
                    "uploader": {
                        "name": "elephant31",
                    },
                },
            ],
        }


class TestDashToken(TestCase):
    def test_full(self):
        """测试全流程"""

        # 获取 access_token 和 refresh_token
        User.objects.create_superuser(username="admin", password="admin", email="")
        response = self.client.post(
            reverse("wallpapers-api:DashTokenAPI"),
            {"username": "admin", "password": "admin"},
            content_type="application/json",
            headers={"CF-Turnstile-Response": "123456"},
        )
        assert response.status_code == 200
        assert "access_token" in response.json()

        # 测试 refresh 接口
        self.client.cookies.load(response.cookies)
        response = self.client.get(
            reverse("wallpapers-api:DashRefreshTokenAPI"),
        )
        assert response.status_code == 200


class TestCaseWithJWTClient(TestCase):
    def setUp(self):
        from wallpapers.auth import generate_access_token

        user = User.objects.create_user(username="testuser", password="testpass123")
        access_token = generate_access_token(user.pk)
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {access_token}"

        super().setUp()


class TestDashPublishTopicAPI(TestCaseWithJWTClient):
    def test_post(self):
        client = models.Client.objects.create()
        topic = models.Topic.objects.create()
        url = reverse(
            "wallpapers-api:DashPublishTopicAPI",
            kwargs={"topic_id": topic.pk, "client_id": client.id.hex},
        )
        response = self.client.post(
            url, {"title": "test"}, content_type="application/json"
        )
        assert response.status_code == 201
        response = self.client.post(
            url, {"title": "test"}, content_type="application/json"
        )
        assert response.status_code == 409
