import random
from unittest import mock

import pytest


@pytest.fixture
def mock_cloudflare_tunstile():
    def random_bool(_):
        return random.choice([True, False])

    async def async_random_bool(_):
        return random_bool(_)

    with (
        mock.patch("wallpapers.utils.cloudflare._verify_sync", new=random_bool),
        mock.patch("wallpapers.utils.cloudflare._verify_async", new=async_random_bool),
    ):
        yield
