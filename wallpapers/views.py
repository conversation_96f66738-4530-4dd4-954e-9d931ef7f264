import enum
import re
import secrets
import typing
import uuid
from typing import Iterable

import httpx
import validators
import zangar as z
from django.conf import settings
from django.contrib.auth import authenticate
from django.core.cache import cache
from django.db.models import Count, F, Model, OuterRef, Q, Subquery, Window
from django.db.models.functions import RowNumber
from django.db.models.query import QuerySet
from django.http import Http404, HttpRequest, HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.views import View
from django.views.decorators.csrf import csrf_exempt

import openapi
from wallpapers.auth import generate_access_token, required_jwt
from wallpapers.utils import cloudflare
from wallpapers.utils.colors import ColorFamily
from wallpapers.utils.imgproxy import get_imgproxy_url

from . import models


class APIView(View):
    @classmethod
    def as_view(cls, **initkwargs):
        view = super().as_view(**initkwargs)
        view = csrf_exempt(view)
        return view


M = typing.TypeVar("M", bound=Model)


def _paginate(queryset: QuerySet[M], page: int, page_size: int) -> QuerySet[M]:
    return queryset[(page - 1) * page_size : page * page_size]


class _SizedformEnum(enum.StrEnum):
    DESKTOP = "desktop"
    MOBILE = "mobile"


def _images_getter(o: models.WallpaperImage):
    if images := o.images:
        return images
    return {"default": get_imgproxy_url(o.url, width=500, height=500)}


_uploader_struct = z.struct(
    {
        "name": z.str(),
    }
)
_wallpaper_struct = z.struct(
    {
        "images": z.field(
            z.ensure(
                lambda x: isinstance(x, dict),
                meta={
                    "oas": {
                        "type": "object",
                        "properties": {
                            "default": {
                                "type": "string",
                            }
                        },
                        "additionalProperties": {
                            "type": "string",
                        },
                        "description": "图片列表，当未提供任何 size 时，将默认填充一个 default 字段",
                    }
                },
            ),
            getter=_images_getter,
        ),
        "format": z.str(),
        "width": z.int(),
        "height": z.int(),
        "filesize": z.int(meta={"oas": {"description": "单位为字节"}}),
        "content_md5": z.str(),
    }
)
_wallpaper_with_id_struct = z.struct(
    {
        **_wallpaper_struct.fields,
        "id": z.int(),
    }
)
_wallpaper_with_uploader_struct = z.struct(
    {
        **_wallpaper_struct.fields,
        "uploader": z.field(
            _uploader_struct,
            getter=lambda o: getattr(o, "uploader"),
        ),
    }
)


def _attach_uploaders(queryset: Iterable[models.WallpaperImage]):
    """为 queryset 中的每个对象附加 uploader 信息

    要求 queryset 中的每个对象都设置上 uploader_id 字段。

    弃用: 改用 _attach_uploader2 函数，快捷方便。
    """
    uploader_ids = [getattr(x, "uploader_id") for x in queryset]
    uploaders = models.Uploader.objects.filter(id__in=uploader_ids)
    uploader_dict = {x.pk: x for x in uploaders}
    for item in queryset:
        item.uploader = uploader_dict[item.uploader_id]  # type: ignore
    return queryset


def _attach_uploaders2(wallpapers: Iterable[models.WallpaperImage]):
    """为每个 wallpaper 附加上 uploader 信息

    无需提前设置 uploader_id 字段
    """
    earliest_uploader = models.UploaderWallpaper.objects.filter(
        wallpaper=OuterRef("wallpaper")
    ).order_by("-pk")
    uploader_wallpapers = models.UploaderWallpaper.objects.filter(
        wallpaper__in=wallpapers, id__in=Subquery(earliest_uploader.values("pk")[:1])
    ).select_related("uploader")
    uploader_dict = {x.wallpaper_id: x.uploader for x in uploader_wallpapers}
    for item in wallpapers:
        item.uploader = uploader_dict.get(item.pk, models.Uploader(name="elephant31"))
    return wallpapers


def _split_size(size: str) -> tuple[int, int]:
    width, height = map(int, size.split("x", 1))
    return width, height


_s_image_sizes = openapi.s_query(
    name="size",
    schema=z.list(
        z.str().ensure(lambda x: bool(re.match(r"^\d+x\d+$", x))).transform(_split_size)
    ),
    py_default=None,
    description="用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址",
)


_s_page = openapi.s_query(name="page", schema=z.to.int().gte(1), py_default=1)
_s_page_size = openapi.s_query(
    name="page_size", schema=z.to.int().gte(1), py_default=10
)


class WallpaperListView(APIView):
    __get_response_schema = z.struct(
        {
            "wallpapers": z.to.list(_wallpaper_with_uploader_struct),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @openapi.declare(
        summary="获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。",
        description="图片为等比例缩放",
    )
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        sizes=_s_image_sizes,
        page=_s_page,
        page_size=_s_page_size,
        filter_sized_for=openapi.s_query(
            name="sized_for",
            schema=z.str().ensure(
                lambda x: x in _SizedformEnum,
                meta={
                    "oas": {
                        "enum": list(_SizedformEnum),
                    }
                },
            ),
            py_default=None,
            description="[filter] 根据尺寸进行过滤",
        ),
        filter_color=openapi.s_query(
            name="color",
            schema=z.str(),
            py_default=None,
            description="[filter] 根据色系进行过滤",
        ),
        filter_tag=openapi.s_query(
            name="tag",
            schema=z.str(),
            py_default=None,
            description="[filter] 根据标签进行过滤",
        ),
    ):
        queryset = (
            models.WallpaperImage.objects.annotate(
                uploader_id=Subquery(
                    models.UploaderWallpaper.objects.filter(
                        wallpaper=OuterRef("pk")
                    ).values("uploader_id")[:1]
                )
            )
            .filter(uploader_id__isnull=False)
            .order_by("-id")
        )

        # filter
        if filter_sized_for is not None:
            if filter_sized_for == _SizedformEnum.DESKTOP:
                queryset = queryset.filter(aspect_ratio__gte=1.2)
            elif filter_sized_for == _SizedformEnum.MOBILE:
                queryset = queryset.filter(aspect_ratio__lt=1.2)
        if filter_color is not None:
            queryset = queryset.filter(imagecolorfamily__color_family=filter_color)
        if filter_tag is not None:
            queryset = queryset.filter(
                cliptag__name=filter_tag,
                cliptag__score__gte=models.TAG_SCORE_THRESHOLD,
            )

        # set images
        for item in queryset:
            if sizes:
                item.set_images(sizes)

        results = _paginate(queryset, page, page_size)
        results = _attach_uploaders(results)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "wallpapers": results,
                    "current_page": page,
                    "current_page_size": page_size,
                    "total": queryset.count(),
                }
            ),
        )


class ImageAPI(APIView):
    @openapi.declare(summary="获取原始图片")
    @openapi.path("key", schema=z.str())
    @openapi.response(200, content={"image/*": openapi.MediaType()})
    @cloudflare.check_tunstile
    async def get(self, request, key):
        try:
            wallpaper = await models.WallpaperImage.objects.aget(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        async with httpx.AsyncClient(timeout=30) as client:
            r = await client.get(wallpaper.url)
            if not r.is_success:
                return HttpResponse(status=404)  # TODO: 图片不可用，该如何处理
            return HttpResponse(r.content, content_type=r.headers["content-type"])


class ColorCategoryAPI(APIView):
    __get_schema = z.struct(
        {
            "colors": z.to.list(
                z.struct(
                    {
                        "value": z.str(),
                        "RGB": z.str(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_schema)},
    )
    async def get(self, request):
        return JsonResponse(
            self.__get_schema.parse(
                {
                    "colors": (x.asdict() for x in ColorFamily),
                }
            )
        )


class TagCategoryAPI(APIView):
    __get_response_schema = z.struct(
        {
            "tags": z.to.list(
                z.struct(
                    {
                        "name": z.str(),
                        "posterUrl": z.str() | z.none(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    def get(self, request):
        names = ["城市", "自然", "动物", "艺术"]

        # 获取每个标签最高分的图片作为 poster，使用窗口函数
        queryset = (
            models.CLIPTag.qualified.filter(name__in=names)
            .annotate(
                row_num=Window(
                    expression=RowNumber(),
                    partition_by=F("name"),
                    order_by=[F("score").desc()],
                )
            )
            .filter(row_num=1)
            .select_related("wallpaper")
        )

        tagname_to_posterURL = {}
        for tag in queryset:
            tagname_to_posterURL[tag.name] = get_imgproxy_url(
                tag.wallpaper.url, width=200, height=200
            )

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "tags": [
                        {
                            "name": name,
                            "posterUrl": tagname_to_posterURL.get(name),
                        }
                        for name in names
                    ]
                }
            )
        )


class RelatedWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "related": z.to.list(_wallpaper_with_uploader_struct),
        }
    )

    @openapi.declare(summary="获取当前壁纸的相关其他壁纸 (固定数量的)")
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.response(404)
    @openapi.path("key", schema=z.str(), description="使用 content_md5 值")
    @openapi.query(
        "num",
        schema=z.to.int(),
        required=False,
        description="返回的数据量最大条数",
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        key,
        num: int = 20,
        sizes=_s_image_sizes,
    ):
        try:
            current_wallpaper = models.WallpaperImage.objects.get(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        related_wallpapers: list[models.WallpaperImage] = []
        tags = current_wallpaper.cliptag_set.order_by("-score").all()
        for tag in tags:
            # 获取有上传者的相关壁纸
            items = (
                models.CLIPTag.qualified.select_related("wallpaper")
                .filter(name=tag.name)
                .exclude(
                    wallpaper__in=[current_wallpaper, *related_wallpapers]
                )  # 不包括自己和已经查出的壁纸
                .annotate(
                    uploader_id=Subquery(
                        models.UploaderWallpaper.objects.filter(
                            wallpaper=OuterRef("wallpaper")
                        ).values("uploader")[:1]
                    )
                )
                .filter(uploader_id__isnull=False)  # 只包含有上传者的壁纸
                .order_by("-score")[: num - len(related_wallpapers)]
            )
            for x in items:
                if sizes:
                    x.wallpaper.set_images(sizes)
                x.wallpaper.uploader_id = x.uploader_id  # type: ignore
                related_wallpapers.append(x.wallpaper)
            if len(related_wallpapers) == num:
                break

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "related": _attach_uploaders(related_wallpapers),
                }
            )
        )


_topic_published_struct = z.struct(
    {
        "id": z.int(),
        "title": z.str(),
    }
)


class TopicsPublishedAPI(APIView):
    __get_response_schema = z.struct({"topics": z.to.list(_topic_published_struct)})

    @openapi.header(
        "client_id",
        name="Client-ID",
        schema=z.str().transform(
            lambda x: uuid.UUID(x, version=4), message="不是一个有效的 Client-ID"
        ),
    )
    @openapi.query("group", schema=z.str(), required=False)
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        client_id: str,
        group: str = "default",
        after=openapi.s_query(
            schema=z.to.int(),
            py_default=None,
        ),
        limit=openapi.s_query(schema=z.to.int().gte(1), py_default=20),
    ):
        topics = models.TopicPublished.objects.filter(
            client_id=client_id, group=group
        ).order_by("-pk")

        if after is not None:
            topics.filter(pk__lt=after)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "topics": topics[:limit],
                }
            )
        )


class TopicWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "topic": _topic_published_struct,
            "wallpapers": z.to.list(_wallpaper_with_uploader_struct),
        }
    )

    @openapi.path("topic_id", schema=z.to.int())
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.response(404)
    @openapi.apply_signature
    def get(self, request, topic_id: int, sizes=_s_image_sizes):
        try:
            topic = models.Topic.objects.get(pk=topic_id)
        except models.Topic.DoesNotExist:
            return HttpResponse(status=404)

        topic_wallpapers = (
            models.TopicWallpaper.objects.filter(topic=topic)
            .select_related("wallpaper")
            .order_by("-pk")
        )

        wallpapers = [i.wallpaper for i in topic_wallpapers]
        if sizes:
            for item in wallpapers:
                item.set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "topic": topic,
                    "wallpapers": _attach_uploaders2(wallpapers),
                }
            )
        )


_REFRESH_TOKEN_COOKIE_KEY = "refresh_token"
_REFRESH_TOKEN_CACHE_KEY = "refresh_token:%s"
_auto_token_response_schema = z.struct(
    {
        "access_token": z.str(),
    }
)
_REFRESH_TOKEN_COOKIE_PATH = (
    "/" if settings.DEBUG else reverse_lazy("wallpapers-api:DashRefreshTokenAPI")
)


class DashTokenAPI(APIView):
    __request_meida_type = openapi.MediaType(
        schema=z.struct(
            {
                "username": z.str(),
                "password": z.str(meta={"oas": {"format": "password"}}),
            }
        )
    )

    @openapi.body(
        "body",
        content={
            "application/x-www-form-urlencoded": __request_meida_type,
            "application/json": __request_meida_type,
        },
    )
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=_auto_token_response_schema)
        },
    )
    @openapi.response(401)
    @cloudflare.check_tunstile
    @openapi.declare(tags=["dashboard"])
    def post(self, request, body):
        user = authenticate(
            request, username=body["username"], password=body["password"]
        )
        if user is None:
            return HttpResponse(status=401)

        refresh_token = secrets.token_urlsafe(64)
        refresh_token_timeout = 60 * 60 * 24 * 30
        cache.set(
            _REFRESH_TOKEN_CACHE_KEY % refresh_token, user.pk, refresh_token_timeout
        )
        response = JsonResponse({"access_token": generate_access_token(user.pk)})
        response.set_cookie(
            key=_REFRESH_TOKEN_COOKIE_KEY,
            value=refresh_token,
            httponly=True,
            path=_REFRESH_TOKEN_COOKIE_PATH,
            secure=not settings.DEBUG,
            max_age=refresh_token_timeout,
        )
        return response


class DashRefreshTokenAPI(APIView):
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=_auto_token_response_schema)
        },
    )
    @openapi.response(401)
    @openapi.declare(tags=["dashboard"])
    def get(self, request: HttpRequest):
        refresh_token = request.COOKIES.get(_REFRESH_TOKEN_COOKIE_KEY)
        if not refresh_token:
            return HttpResponse(status=401)
        user_id = cache.get(_REFRESH_TOKEN_CACHE_KEY % refresh_token, default=None)
        if user_id is None:
            return HttpResponse(status=401)
        return JsonResponse({"access_token": generate_access_token(user_id)})


class DashRevokeTokenAPI(APIView):
    @openapi.response(200)
    @openapi.declare(tags=["dashboard"])
    def get(self, request: HttpRequest):
        refresh_token = request.COOKIES.get(_REFRESH_TOKEN_COOKIE_KEY)
        if refresh_token:
            cache.delete(_REFRESH_TOKEN_CACHE_KEY % refresh_token)
        response = HttpResponse(status=200)
        response.delete_cookie(
            _REFRESH_TOKEN_COOKIE_KEY, path=_REFRESH_TOKEN_COOKIE_PATH
        )
        return response


class DashUploadWallapersAPI(APIView):
    @openapi.response(200)
    @openapi.declare(tags=["dashboard"])
    @required_jwt
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(
                schema=z.struct(
                    {
                        "urls": z.list(
                            z.str().ensure(lambda x: validators.url(x) is True)
                        ).ensure(lambda x: len(x) > 0, message="最少要提供一个 URL")
                    }
                )
            )
        },
    )
    def post(self, request, body):
        from wallpapers.services.rabbitmq import publish_wallpapers

        publish_wallpapers(set(body["urls"]))
        return JsonResponse(body)


_topic_struct = z.struct(
    {
        "id": z.int(),
        "comment": z.str(),
    }
)

_post_topic_struct = z.struct(
    z.omit_fields(_topic_struct.fields, ["id"]),
)
_put_topic_struct = z.struct(z.required_fields(_post_topic_struct.fields))


class DashTopicListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "topics": z.to.list(
                z.struct(
                    {
                        **_topic_struct.fields,
                        "wallpaper_count": z.int(),
                        "published_count": z.int(
                            meta={"oas": {"description": "已发布统计"}}
                        ),
                    }
                )
            ),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        page=_s_page,
        page_size=_s_page_size,
    ):
        queryset = (
            models.Topic.objects.annotate(
                wallpaper_count=Count("topicwallpaper"),
                published_count=Count("topicpublished"),
            )
            .order_by("-pk")
            .all()
        )
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "topics": _paginate(queryset, page, page_size),
                    "current_page": page,
                    "current_page_size": page_size,
                    "total": queryset.count(),
                }
            )
        )

    @openapi.response(
        200, content={"application/json": openapi.MediaType(schema=_topic_struct)}
    )
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(schema=_post_topic_struct),
        },
    )
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    def post(self, request, body):
        obj = models.Topic.objects.create(**body)
        return JsonResponse(_topic_struct.parse(obj))


_path_topic_id = openapi.path("topic_id", schema=z.to.int())


def _get_topic(topic_id):
    try:
        return models.Topic.objects.get(pk=topic_id)
    except models.Topic.DoesNotExist:
        raise Http404


def _get_client(client_id):
    try:
        return models.Client.objects.get(pk=client_id)
    except models.Client.DoesNotExist:
        raise Http404


class DashTopicDetailAPI(APIView):
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(schema=_put_topic_struct),
        },
    )
    @openapi.response(
        200, content={"application/json": openapi.MediaType(schema=_topic_struct)}
    )
    @required_jwt
    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    def put(self, request, topic_id, body: dict):
        topic = _get_topic(topic_id)
        for k, v in body.items():
            setattr(topic, k, v)
        topic.save()
        return JsonResponse(_topic_struct.parse(topic))

    @openapi.response(204)
    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    @required_jwt
    def delete(self, request, topic_id):
        topic = _get_topic(topic_id)
        topic.delete()
        return HttpResponse(status=204)


class DashTopicWallaperListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "topic": _topic_struct,
            "wallpapers": z.to.list(_wallpaper_with_id_struct),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @required_jwt
    @openapi.apply_signature
    def get(self, request, topic_id, page=_s_page, page_size=_s_page_size):
        topic = _get_topic(topic_id)
        topic_wallpapers = (
            models.TopicWallpaper.objects.filter(topic=topic)
            .select_related("wallpaper")
            .order_by("-pk")
        )
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "topic": topic,
                    "wallpapers": (
                        x.wallpaper
                        for x in _paginate(topic_wallpapers, page, page_size)
                    ),
                    "current_page": page,
                    "current_page_size": page_size,
                    "total": topic_wallpapers.count(),
                }
            )
        )


_path_wallpaper_id = openapi.path("wallpaper_id", schema=z.to.int())


def _get_wallpaper(wallpaper_id: int):
    try:
        return models.WallpaperImage.objects.get(pk=wallpaper_id)
    except models.WallpaperImage.DoesNotExist:
        raise Http404


class DashTopicWallaperDetailAPI(APIView):
    @openapi.response(200)
    @openapi.declare(tags=["dashboard"], summary="将壁纸与话题关联")
    @_path_topic_id
    @_path_wallpaper_id
    @required_jwt
    def put(self, request, topic_id, wallpaper_id):
        topic = _get_topic(topic_id)
        wallpaper = _get_wallpaper(wallpaper_id)
        models.TopicWallpaper.objects.get_or_create(topic=topic, wallpaper=wallpaper)
        return HttpResponse()

    @_path_topic_id
    @_path_wallpaper_id
    @required_jwt
    @openapi.response(204)
    @openapi.declare(tags=["dashboard"], summary="取消壁纸与话题的关联")
    def delete(self, request, topic_id, wallpaper_id):
        topic = _get_topic(topic_id)
        wallpaper = _get_wallpaper(wallpaper_id)
        models.TopicWallpaper.objects.filter(topic=topic, wallpaper=wallpaper).delete()
        return HttpResponse(status=204)


class DashSearchWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "q": z.str(),
            "results": z.to.list(
                z.struct(
                    {
                        "id": z.int(),
                        "wallpaper": _wallpaper_with_id_struct,
                    }
                )
            ),
        }
    )

    @required_jwt
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        query=openapi.s_query(
            schema=z.str().strip().min(1),
            name="q",
        ),
        after=openapi.s_query(
            schema=z.to.int()
            .transform(lambda x: models.CLIPTag.objects.filter(pk=x).first())
            .ensure(lambda x: x is not None),
            py_default=None,
        ),
        limit=openapi.s_query(schema=z.to.int().gte(1), py_default=20),
        sizes=_s_image_sizes,
        exclude_topic_id=openapi.s_query(
            schema=z.to.int(), py_default=None, description="排除指定专题下的壁纸"
        ),
    ):
        queryset = (
            models.CLIPTag.qualified.filter(name__startswith=query)
            .select_related("wallpaper")
            .order_by("-score", "-pk")
        )
        if after is not None:
            queryset = queryset.filter(
                Q(score__lt=after.score) | Q(score=after.score, pk__lt=after.score)
            )

        if exclude_topic_id is not None:
            queryset = queryset.exclude(
                wallpaper__topicwallpaper__topic_id=exclude_topic_id
            )

        queryset = queryset[:limit]

        if sizes:
            for item in queryset:
                item.wallpaper.set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "q": query,
                    "results": queryset,
                }
            )
        )


class DashTopicSuggestedTagsAPI(APIView):
    __get_response_schema = z.struct(
        {
            "tags": z.to.list(z.str()),
        }
    )

    @_path_topic_id
    @openapi.declare(tags=["dashboard"], summary="获取 Topic 可能的标签")
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @required_jwt
    def get(self, request, topic_id):
        topic = _get_topic(topic_id)
        random_topic_wallpapers = (
            models.TopicWallpaper.objects.filter(topic=topic)
            .select_related("wallpaper")
            .order_by("?")[:10]
        )
        tags = (
            models.CLIPTag.qualified.filter(
                wallpaper__in=(item.wallpaper for item in random_topic_wallpapers)
            )
            .values_list("name", flat=True)
            .order_by("-score")[:20]
        )
        return JsonResponse(self.__get_response_schema.parse({"tags": set(tags)}))


class DashPublishTopicAPI(APIView):
    @_path_topic_id
    @openapi.path("client_id", schema=z.str())
    @required_jwt
    @openapi.response(201)
    @openapi.declare(tags=["dashboard"], summary="发布专题")
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(
                schema=z.struct(
                    {
                        "title": z.str().strip().min(1),
                        # "group": z.field(z.str().strip().min(1)).optional(), # 先不使用 group 看看效果
                    }
                )
            )
        },
    )
    @openapi.response(409)
    def post(self, request, topic_id, client_id, body: dict):
        topic = _get_topic(topic_id)
        client = _get_client(client_id)
        obj, created = models.TopicPublished.objects.get_or_create(
            topic=topic,
            client=client,
            defaults={"title": body["title"]},
        )
        if not created:
            return HttpResponse(status=409)
        return HttpResponse(status=201)


class DashTopicPublishedListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "published": z.to.list(
                z.struct(
                    {
                        "id": z.int(),
                        "title": z.str(),
                        "topic": _topic_struct,
                        "client": z.struct(
                            {
                                "id": z.to.str(),
                                "name": z.str(),
                            }
                        ),
                        "published_at": z.datetime(),
                    }
                )
            ),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    @openapi.declare(tags=["dashboard"])
    @required_jwt
    def get(self, request, page=_s_page, page_size=_s_page_size):
        queryset = models.TopicPublished.objects.select_related(
            "topic", "client"
        ).order_by("-pk")
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "published": _paginate(queryset, page, page_size),
                    "current_page": page,
                    "current_page_size": page_size,
                    "total": queryset.count(),
                }
            )
        )


class DashTopicPublishedDetailAPI(APIView):
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    @openapi.path("published_id", schema=z.to.int())
    @openapi.response(204)
    def delete(self, request, published_id):
        models.TopicPublished.objects.filter(pk=published_id).delete()
        return HttpResponse(status=204)


class DashClientListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "clients": z.to.list(
                z.struct(
                    {
                        "id": z.to.str(),
                        "name": z.str(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    def get(self, request):
        queryset = models.Client.objects.all()
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "clients": queryset,
                }
            )
        )
