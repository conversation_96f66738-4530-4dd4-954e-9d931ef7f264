import enum
import re
from dataclasses import dataclass, field
from typing import Iterable

import httpx
import zangar as z
from django.db.models import F, OuterRef, Subquery, Window
from django.db.models.functions import RowNumber
from django.db.models.query import QuerySet
from django.http import HttpResponse, JsonResponse
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt

import openapi
from wallpapers.utils import cloudflare
from wallpapers.utils.colors import ColorFamily
from wallpapers.utils.imgproxy import get_imgproxy_url

from . import models


@method_decorator(csrf_exempt, name="dispatch")
class APIView(View):
    pass


@dataclass
class _Paging:
    page: int = field(default=1, metadata={"zangar": {"schema": z.to.int().gte(1)}})
    page_size: int = field(
        default=10, metadata={"zangar": {"schema": z.to.int().gte(1)}}
    )

    def paginate(self, queryset: QuerySet) -> QuerySet:
        return queryset[(self.page - 1) * self.page_size : self.page * self.page_size]


class _SizedformEnum(enum.StrEnum):
    DESKTOP = "desktop"
    MOBILE = "mobile"


_IMAGES_FIELD = "images"


def _images_getter(o: models.WallpaperImage):
    if images := getattr(o, _IMAGES_FIELD, None):
        return images
    return {"default": get_imgproxy_url(o.url, width=500, height=500)}


_uploader_struct = z.struct(
    {
        "name": z.str(),
    }
)
_wallpaper_struct = z.struct(
    {
        # "url": z.str(meta={"oas": {"description": "原图地址"}}),
        _IMAGES_FIELD: z.field(
            z.ensure(
                lambda x: isinstance(x, dict),
                meta={
                    "oas": {
                        "type": "object",
                        "properties": {
                            "default": {
                                "type": "string",
                            }
                        },
                        "additionalProperties": {
                            "type": "string",
                        },
                        "description": "图片列表，当未提供任何 size 时，将默认填充一个 default 字段",
                    }
                },
            ),
            getter=_images_getter,
        ),
        "format": z.str(),
        "width": z.int(),
        "height": z.int(),
        "filesize": z.int(meta={"oas": {"description": "单位为字节"}}),
        "content_md5": z.str(),
        "uploader": z.field(
            _uploader_struct,
            getter=lambda o: getattr(o, "uploader"),
        ),
    }
)


def _attach_uploaders(queryset: Iterable[models.WallpaperImage]):
    """为 queryset 中的每个对象附加 uploader 信息

    要求 queryset 中的每个对象都设置上 uploader_id 字段
    """
    uploader_ids = [getattr(x, "uploader_id") for x in queryset]
    uploaders = models.Uploader.objects.filter(id__in=uploader_ids)
    uploader_dict = {x.pk: x for x in uploaders}
    for item in queryset:
        item.uploader = uploader_dict[item.uploader_id]  # type: ignore
    return queryset


_image_sizes_query = openapi.query(
    "sizes",
    name="size",
    schema=z.list(
        z.str()
        .ensure(lambda x: bool(re.match(r"^\d+x\d+$", x)))
        .transform(lambda x: tuple(map(int, x.split("x"))))
    ),
    required=False,
    description="用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址",
)


def _set_images(obj: models.WallpaperImage, sizes: Iterable[tuple[int, int]]):
    images = {}
    for size in sizes:
        width, height = size
        images[f"{width}x{height}"] = get_imgproxy_url(
            obj.url,
            width=width,
            height=height,
        )
    setattr(obj, _IMAGES_FIELD, images)


class WallpaperListView(APIView):
    @dataclass
    class __Filters:
        sized_for: _SizedformEnum | None = field(
            default_factory=lambda: None,
            metadata={
                "zangar": {
                    "schema": z.str().ensure(
                        lambda x: x in _SizedformEnum,
                        meta={
                            "oas": {
                                "enum": list(_SizedformEnum),
                            }
                        },
                    )
                }
            },
        )
        color: ColorFamily | None = field(
            default_factory=lambda: None,
            metadata={
                "zangar": {
                    "schema": z.str().ensure(
                        lambda x: x in ColorFamily,
                        meta={
                            "oas": {
                                "enum": list(ColorFamily),
                            }
                        },
                    )
                }
            },
        )
        tag: str | None = field(
            default_factory=lambda: None, metadata={"zangar": {"schema": z.str()}}
        )

    __get_response_schema = z.struct(
        {
            "wallpapers": z.to.list(_wallpaper_struct),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @openapi.declare(
        summary="获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。",
        description="图片为等比例缩放",
    )
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.response(400)
    @_image_sizes_query
    @openapi.query(
        "paging",
        schema=z.dataclass(_Paging),
        required=False,
    )
    @openapi.query(
        "filters",
        schema=z.dataclass(__Filters),
        required=False,
    )
    def get(
        self,
        request,
        sizes: list[tuple[int, int]] | None = None,
        paging: _Paging = _Paging(),
        filters: __Filters = __Filters(),
    ):
        queryset = (
            models.WallpaperImage.objects.annotate(
                uploader_id=Subquery(
                    models.UploaderWallpaper.objects.filter(
                        wallpaper=OuterRef("pk")
                    ).values("uploader_id")[:1]
                )
            )
            .filter(uploader_id__isnull=False)
            .order_by("-id")
        )

        # filter
        if filters:
            if filters.sized_for is not None:
                if filters.sized_for == _SizedformEnum.DESKTOP:
                    queryset = queryset.filter(aspect_ratio__gte=1.2)
                elif filters.sized_for == _SizedformEnum.MOBILE:
                    queryset = queryset.filter(aspect_ratio__lt=1.2)

            if filters.color is not None:
                queryset = queryset.filter(imagecolorfamily__color_family=filters.color)

            if filters.tag is not None:
                queryset = queryset.filter(
                    cliptag__name=filters.tag,
                    cliptag__score__gte=models.TAG_SCORE_THRESHOLD,
                )

        # set images
        for item in queryset:
            if sizes:
                _set_images(item, sizes)

        results = paging.paginate(queryset)
        results = _attach_uploaders(results)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "wallpapers": results,
                    "current_page": paging.page,
                    "current_page_size": paging.page_size,
                    "total": queryset.count(),
                }
            ),
        )


class ImageAPI(APIView):
    @openapi.declare(summary="获取原始图片")
    @openapi.path("key", schema=z.str())
    @openapi.response(200, content={"image/*": openapi.MediaType()})
    @openapi.response(404)
    @cloudflare.check_tunstile
    async def get(self, request, key):
        try:
            wallpaper = await models.WallpaperImage.objects.aget(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        async with httpx.AsyncClient(timeout=30) as client:
            r = await client.get(wallpaper.url)
            if not r.is_success:
                return HttpResponse(status=404)  # TODO: 图片不可用，该如何处理
            return HttpResponse(r.content, content_type=r.headers["content-type"])


class ColorCategoryAPI(APIView):
    __get_schema = z.struct(
        {
            "colors": z.to.list(
                z.struct(
                    {
                        "value": z.str(),
                        "RGB": z.str(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_schema)},
    )
    async def get(self, request):
        return JsonResponse(
            self.__get_schema.parse(
                {
                    "colors": (x.asdict() for x in ColorFamily),
                }
            )
        )


class TagCategoryAPI(APIView):
    __get_response_schema = z.struct(
        {
            "tags": z.to.list(
                z.struct(
                    {
                        "name": z.str(),
                        "posterUrl": z.str() | z.none(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    def get(self, request):
        names = ["城市", "自然", "动物", "艺术"]

        # 获取每个标签最高分的图片作为 poster，使用窗口函数
        queryset = (
            models.CLIPTag.qualified.filter(name__in=names)
            .annotate(
                row_num=Window(
                    expression=RowNumber(),
                    partition_by=F("name"),
                    order_by=[F("score").desc()],
                )
            )
            .filter(row_num=1)
            .select_related("wallpaper")
        )

        tagname_to_posterURL = {}
        for tag in queryset:
            tagname_to_posterURL[tag.name] = get_imgproxy_url(
                tag.wallpaper.url, width=200, height=200
            )

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "tags": [
                        {
                            "name": name,
                            "posterUrl": tagname_to_posterURL.get(name),
                        }
                        for name in names
                    ]
                }
            )
        )


class RelatedWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "related": z.to.list(_wallpaper_struct),
        }
    )

    @openapi.declare(summary="获取当前壁纸的相关其他壁纸 (固定数量的)")
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.response(400)
    @openapi.response(404)
    @openapi.path("key", schema=z.str(), description="使用 content_md5 值")
    @openapi.query(
        "num",
        schema=z.to.int(),
        required=False,
        description="返回的数据量最大条数",
    )
    @_image_sizes_query
    def get(
        self, request, key, num: int = 20, sizes: list[tuple[int, int]] | None = None
    ):
        try:
            current_wallpaper = models.WallpaperImage.objects.get(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        related_wallpapers: list[models.WallpaperImage] = []
        tags = current_wallpaper.cliptag_set.order_by("-score").all()
        for tag in tags:
            # 获取有上传者的相关壁纸
            items = (
                models.CLIPTag.qualified.select_related("wallpaper")
                .filter(name=tag.name)
                .exclude(
                    wallpaper__in=[current_wallpaper, *related_wallpapers]
                )  # 不包括自己和已经查出的壁纸
                .annotate(
                    uploader_id=Subquery(
                        models.UploaderWallpaper.objects.filter(
                            wallpaper=OuterRef("wallpaper")
                        ).values("uploader")[:1]
                    )
                )
                .filter(uploader_id__isnull=False)  # 只包含有上传者的壁纸
                .order_by("-score")[: num - len(related_wallpapers)]
            )
            for x in items:
                if sizes:
                    _set_images(x.wallpaper, sizes)
                x.wallpaper.uploader_id = x.uploader_id  # type: ignore
                related_wallpapers.append(x.wallpaper)
            if len(related_wallpapers) == num:
                break

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "related": _attach_uploaders(related_wallpapers),
                }
            )
        )
