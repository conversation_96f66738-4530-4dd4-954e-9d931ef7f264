from django.http import HttpRequest
from django.shortcuts import redirect, render
from django.urls import include, path, reverse

from openapi.routing import Router
from wallpapers.auth import OAS_SECURITY_KEY

from . import views


class MyRouter(Router):
    def spec(self):
        spec = super().spec()
        spec.update(
            {
                "info": {
                    "title": "Wallpapers API",
                    "version": "0.1.0",
                },
                "components": {
                    "securitySchemes": {
                        OAS_SECURITY_KEY: {
                            "type": "http",
                            "scheme": "bearer",
                            "bearerFormat": "JWT",
                        }
                    },
                },
            }
        )
        return spec


router = MyRouter()
router.add_url("/image/{key}", views.ImageAPI)
router.add_url("/wallpapers", views.WallpaperListView)
router.add_url("/wallpapers/{key}/related", views.RelatedWallpapersAPI)
router.add_url("/categories/colors", views.ColorCategoryAPI)
router.add_url("/categories/tags", views.TagCategoryAPI)
router.add_url("/topics", views.TopicsPublishedAPI)
router.add_url("/topics/{topic_id}/wallpapers", views.TopicWallpapersAPI)

router.add_url("/dash/auth/token", views.DashTokenAPI)
router.add_url("/dash/auth/token/refresh", views.DashRefreshTokenAPI)
router.add_url("/dash/auth/token/revoke", views.DashRevokeTokenAPI)
router.add_url("/dash/uploadwallpapers", views.DashUploadWallapersAPI)
router.add_url("/dash/topics", views.DashTopicListAPI)
router.add_url("/dash/topics/{topic_id}", views.DashTopicDetailAPI)
router.add_url("/dash/topics/{topic_id}/wallpapers", views.DashTopicWallaperListAPI)
router.add_url(
    "/dash/topics/{topic_id}/wallpapers/{wallpaper_id}",
    views.DashTopicWallaperDetailAPI,
)
router.add_url("/dash/topicpublished", views.DashTopicPublishedListAPI)
router.add_url("/dash/topicpublished/{published_id}", views.DashTopicPublishedDetailAPI)
router.add_url("/dash/topic/{topic_id}/publish/{client_id}", views.DashPublishTopicAPI)
router.add_url(
    "/dash/topics/{topic_id}/suggestions/tags", views.DashTopicSuggestedTagsAPI
)
router.add_url("/dash/search/wallpapers", views.DashSearchWallpapersAPI)
router.add_url("/dash/clients", views.DashClientListAPI)


def index(request):
    return redirect("wallpapers-api:apidoc")


def apidoc(request: HttpRequest):
    return render(
        request,
        "swagger-ui.html",
        context={
            "config": {"url": reverse("wallpapers-api:openapi")},
        },
    )


app_name = "wallpapers-api"
urlpatterns = [
    path("", index, name="index"),
    path("apidoc/", apidoc, name="apidoc"),
    path("", include(router.urls)),
]
