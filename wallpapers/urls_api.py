from django.http import HttpRequest
from django.shortcuts import redirect, render
from django.urls import include, path, reverse

from openapi.routing import Router

from . import views


class MyRouter(Router):
    openapi_spec = {
        "info": {
            "title": "Wallpapers API",
            "version": "0.1.0",
        }
    }


router = MyRouter()
router.add_url("/image/{key}", views.ImageAPI)
router.add_url("/wallpapers", views.WallpaperListView)
router.add_url("/wallpapers/{key}/related", views.RelatedWallpapersAPI)
router.add_url("/categories/colors", views.ColorCategoryAPI)
router.add_url("/categories/tags", views.TagCategoryAPI)


def index(request):
    return redirect("wallpapers-api:apidoc")


def apidoc(request: HttpRequest):
    return render(
        request,
        "swagger-ui.html",
        context={
            "config": {"url": reverse("wallpapers-api:openapi")},
        },
    )


app_name = "wallpapers-api"
urlpatterns = [
    path("", index, name="index"),
    path("apidoc/", apidoc, name="apidoc"),
    path("", include(router.urls)),
]
