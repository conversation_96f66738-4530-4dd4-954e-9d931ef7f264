import datetime
import functools
from inspect import iscoroutinefunction

import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from django.http import HttpRequest, HttpResponse

import openapi

User = get_user_model()
OAS_SECURITY_KEY = "access_token"  # 用于标记 OpenAPI securiry
_ALGORITHM = "HS256"
_SECRET_KEY = settings.SECRET_KEY


def required_jwt(func):
    if iscoroutinefunction(func):
        raise NotImplementedError
    else:

        @openapi.declare(security=[{OAS_SECURITY_KEY: []}])
        @openapi.response(401)
        @functools.wraps(func)
        def wrapper(this, request: HttpRequest, *args, **kwargs):
            scheme, _, token = request.headers.get("Authorization", "").partition(" ")
            if scheme != "Bearer" or not token:
                return HttpResponse(status=401)

            try:
                jwt.decode(token, _SECRET_KEY, algorithms=[_ALGORITHM])
            except jwt.InvalidTokenError:
                return HttpResponse(status=401)

            return func(this, request, *args, **kwargs)

    return wrapper


def generate_access_token(user_id):
    now = datetime.datetime.now(datetime.timezone.utc)
    payload = {
        "user_id": user_id,
        "exp": now + datetime.timedelta(minutes=5),
        "iat": now,
    }
    return jwt.encode(payload, _SECRET_KEY, algorithm=_ALGORITHM)
