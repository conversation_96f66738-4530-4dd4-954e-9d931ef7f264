import io
import logging
from dataclasses import dataclass
from hashlib import md5

import httpx
import PIL.Image
import PIL.ImageFile
from django.db import transaction

from wallpapers import models
from wallpapers.services.rabbitmq import publish_wallpaper_inbound_event
from wallpapers.utils.colors import get_color_family_historgram

logger = logging.getLogger(__name__)


async def async_download_image(wallpaper: models.WallpaperImage):
    content = await async_download_url(wallpaper.url)
    return PIL.Image.open(io.BytesIO(content))


def download_url(url: str) -> bytes:
    response = httpx.get(url)
    if not response.is_success:
        raise ValueError(f"Failed to download image: {response.url}")
    return response.content


async def async_download_url(url: str) -> bytes:
    async with httpx.AsyncClient() as client:
        r = await client.get(url)
        if not r.is_success:
            raise ValueError(f"Failed to download image: {r.url}")
        return r.content


@dataclass
class WallpaperImageData:
    url: str
    image: PIL.ImageFile.ImageFile
    content: bytes


def download_image(url):
    content = download_url(url)
    image = PIL.Image.open(io.BytesIO(content))
    return WallpaperImageData(
        url=url,
        image=image,
        content=content,
    )


def save_wallpaper(
    wallpaper: WallpaperImageData,
    uploader: models.Uploader | None = None,
):
    width, height = wallpaper.image.size
    aspect_ratio = width / height
    pixels = width * height
    filesize = len(wallpaper.content)
    content_md5 = md5(wallpaper.content).hexdigest()
    assert wallpaper.image.format is not None

    with transaction.atomic():
        # 使用 get_or_create 来避免重复图片的错误
        obj, created = models.WallpaperImage.objects.get_or_create(
            content_md5=content_md5,
            defaults={
                "url": wallpaper.url,
                "format": wallpaper.image.format.lower(),
                "width": width,
                "height": height,
                "aspect_ratio": aspect_ratio,
                "pixels": pixels,
                "filesize": filesize,
            },
        )
        if created:
            logger.info("新增壁纸: %s", obj.url)
            publish_wallpaper_inbound_event(obj)
        else:
            logger.info("已存在壁纸: %s", obj.url)

        if uploader:
            models.UploaderWallpaper.objects.get_or_create(
                uploader=uploader, wallpaper=obj
            )
        return obj


def _get_color_family(url: str):
    content = download_url(url)
    color_family_hist = get_color_family_historgram(io.BytesIO(content))
    logger.info("壁纸 %s 色系分布: %s", url, color_family_hist)
    sorted_color_families = sorted(
        color_family_hist.items(), key=lambda x: x[1], reverse=True
    )
    if sorted_color_families:
        color_family = sorted_color_families[0]
        if color_family[1] >= 0.55:
            return color_family[0].value


def color_family_job(url: str):
    """获取还未设置色系的壁纸，设置色系"""
    wallpapers = models.WallpaperImage.objects.filter(url=url)
    if not wallpapers:
        return

    color_family = _get_color_family(url)
    if color_family:
        for wallpaper in wallpapers:
            models.ImageColorFamily.objects.update_or_create(
                wallpaper=wallpaper, color_family=color_family
            )


def get_top_tags(n):
    from django.db.models import Count

    return (
        models.CLIPTag.qualified.values("name")
        .annotate(count=Count("id"))
        .order_by("-count")[:n]
    )


def random_uploader() -> models.Uploader | None:
    """随机获取一个上传者"""
    return models.Uploader.objects.filter(email=None).order_by("?").first()
